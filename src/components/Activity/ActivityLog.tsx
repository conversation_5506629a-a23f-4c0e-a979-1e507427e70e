import React from 'react';
import { Activity } from 'lucide-react';

const ActivityLog: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Activity Log</h1>
        <p className="text-gray-600 dark:text-gray-300">View all your recent activities and changes</p>
      </div>

      <div className="card text-center py-12">
        <Activity size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Activity Log</h3>
        <p className="text-gray-600 dark:text-gray-300">
          This will show a comprehensive log of all activities including group creation, expense additions, and more.
        </p>
      </div>
    </div>
  );
};

export default ActivityLog;
