import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';

export const testFirestoreConnection = async () => {
  try {
    console.log('Testing Firestore connection...');
    
    // Try to write a test document
    const testRef = doc(db, 'test', 'connection');
    await setDoc(testRef, {
      message: 'Hello Firestore!',
      timestamp: new Date(),
      test: true
    });
    
    console.log('✅ Successfully wrote to Firestore');
    
    // Try to read the document back
    const docSnap = await getDoc(testRef);
    if (docSnap.exists()) {
      console.log('✅ Successfully read from Firestore:', docSnap.data());
      return true;
    } else {
      console.log('❌ Document does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Firestore connection failed:', error);
    return false;
  }
};

// Call this function to test the connection
if (typeof window !== 'undefined') {
  // Only run in browser environment
  setTimeout(() => {
    testFirestoreConnection();
  }, 2000);
}
