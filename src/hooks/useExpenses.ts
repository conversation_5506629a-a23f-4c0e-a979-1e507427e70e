import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { expenseService, personalExpenseService } from '../services/expenseService';
import type { Expense, PersonalExpense } from '../types';

export const useGroupExpenses = (groupId: string | null) => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!groupId) {
      setExpenses([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = expenseService.onGroupExpensesSnapshot(
      groupId,
      (updatedExpenses) => {
        setExpenses(updatedExpenses);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [groupId]);

  const addExpense = async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setError(null);
      const expenseId = await expenseService.createGroupExpense(expenseData);
      return expenseId;
    } catch (err) {
      const errorMessage = 'Failed to add expense';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateExpense = async (expenseId: string, updates: Partial<Omit<Expense, 'id' | 'createdAt'>>) => {
    try {
      setError(null);
      await expenseService.updateExpense(expenseId, updates);
    } catch (err) {
      const errorMessage = 'Failed to update expense';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteExpense = async (expenseId: string) => {
    try {
      setError(null);
      await expenseService.deleteExpense(expenseId);
    } catch (err) {
      const errorMessage = 'Failed to delete expense';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return {
    expenses,
    loading,
    error,
    addExpense,
    updateExpense,
    deleteExpense
  };
};

export const usePersonalExpenses = (month?: string) => {
  const { currentUser } = useAuth();
  const [expenses, setExpenses] = useState<PersonalExpense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) {
      setExpenses([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = personalExpenseService.onUserExpensesSnapshot(
      currentUser.uid,
      (updatedExpenses) => {
        // Filter by month if specified
        const filteredExpenses = month 
          ? updatedExpenses.filter(expense => expense.month === month)
          : updatedExpenses;
        
        setExpenses(filteredExpenses);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [currentUser, month]);

  const addExpense = async (expenseData: Omit<PersonalExpense, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setError(null);
      const expenseId = await personalExpenseService.create(expenseData);
      return expenseId;
    } catch (err) {
      const errorMessage = 'Failed to add personal expense';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateExpense = async (expenseId: string, updates: Partial<Omit<PersonalExpense, 'id' | 'createdAt'>>) => {
    try {
      setError(null);
      await personalExpenseService.update(expenseId, updates);
    } catch (err) {
      const errorMessage = 'Failed to update personal expense';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteExpense = async (expenseId: string) => {
    try {
      setError(null);
      await personalExpenseService.delete(expenseId);
    } catch (err) {
      const errorMessage = 'Failed to delete personal expense';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return {
    expenses,
    loading,
    error,
    addExpense,
    updateExpense,
    deleteExpense
  };
};

export const useUserExpenses = () => {
  const { currentUser } = useAuth();
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) {
      setExpenses([]);
      setLoading(false);
      return;
    }

    const fetchExpenses = async () => {
      try {
        setLoading(true);
        setError(null);
        const userExpenses = await expenseService.getUserExpenses(currentUser.uid);
        setExpenses(userExpenses);
      } catch (err) {
        setError('Failed to load expenses');
        console.error('Error fetching user expenses:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchExpenses();
  }, [currentUser]);

  return {
    expenses,
    loading,
    error,
    refetch: () => {
      if (currentUser) {
        setLoading(true);
        // Re-fetch expenses
      }
    }
  };
};
