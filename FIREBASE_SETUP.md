# Firebase Setup Guide

## 🔥 Firebase Console Setup

Your Firebase project is already configured, but you need to enable Firestore database. Follow these steps:

### 1. Go to Firebase Console
Visit: https://console.firebase.google.com/project/retro-app-2025

### 2. Enable Firestore Database
1. Click on "Firestore Database" in the left sidebar
2. Click "Create database"
3. Choose "Start in test mode" (we'll add security rules later)
4. Select a location (choose the closest to your users)
5. Click "Done"

### 3. Enable Authentication
1. Click on "Authentication" in the left sidebar
2. Go to "Sign-in method" tab
3. Enable "Google" provider
4. Add your domain (localhost for development)
5. Save the configuration

### 4. Security Rules (Optional for now)
The app will work in test mode initially. Later, you can add these security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🚀 Testing the Connection

Once Firestore is enabled:

1. Open the app: http://localhost:5173
2. Sign in with Google
3. Check the browser console for connection test results
4. You should see: "✅ Successfully wrote to Firestore"

## 🔧 Current Configuration

Your app is configured with:
- **Project ID**: retro-app-2025
- **Auth Domain**: retro-app-2025.firebaseapp.com
- **Admin Email**: <EMAIL>

## 📝 Next Steps

After Firestore is enabled:
1. The authentication will work properly
2. You can create groups and add expenses
3. Real-time updates will function
4. Admin panel will be accessible

## 🐛 Troubleshooting

If you still see connection errors:
1. Make sure Firestore is enabled in the Firebase console
2. Check that the project ID matches in .env file
3. Verify authentication is enabled
4. Clear browser cache and reload

## 🔒 Security Notes

- Test mode allows all reads/writes (good for development)
- Production apps should use proper security rules
- Admin features are <NAME_EMAIL>
- All user data is properly isolated when security rules are applied
