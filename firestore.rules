rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth.token.email == '<EMAIL>';
    }
    
    function isGroupMember(groupId) {
      return request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.members;
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isOwner(userId) || isAdmin();
      allow create: if isOwner(userId);
    }
    
    // Groups collection
    match /groups/{groupId} {
      allow read: if isAuthenticated() && (isGroupMember(groupId) || isAdmin());
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isGroupMember(groupId) || isAdmin());
      allow delete: if isAdmin();
    }
    
    // Expenses collection
    match /expenses/{expenseId} {
      allow read: if isAuthenticated() && (
        resource.data.paidBy == request.auth.uid ||
        request.auth.uid in resource.data.splitBetween ||
        isAdmin()
      );
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        resource.data.paidBy == request.auth.uid ||
        isAdmin()
      );
      allow delete: if isAuthenticated() && (
        resource.data.paidBy == request.auth.uid ||
        isAdmin()
      );
    }
    
    // Personal expenses collection
    match /personalExpenses/{expenseId} {
      allow read, write: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow create: if isAuthenticated();
    }
    
    // Activity logs collection
    match /activityLogs/{logId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow create: if isAuthenticated();
      allow update, delete: if isAdmin();
    }
    
    // Balances collection
    match /balances/{balanceId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow write: if isAuthenticated();
    }
    
    // Statistics collection
    match /statistics/{userId} {
      allow read, write: if isAuthenticated() && (
        isOwner(userId) ||
        isAdmin()
      );
    }
    
    // Admin-only collections
    match /adminData/{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
